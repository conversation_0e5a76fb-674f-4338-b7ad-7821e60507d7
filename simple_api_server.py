#!/usr/bin/env python3
"""
Simple HTTP API Server for Image Generation
可以被Cursor和Augment通过HTTP请求调用
"""

import asyncio
import json
import logging
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from tools.image_generator import create_image_generator, ImageGeneratorTool
from config.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Image Generation API",
    description="Simple API for generating images using APIIHUB",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global image generator
image_generator: ImageGeneratorTool = None


class ImageRequest(BaseModel):
    """Request model for single image generation."""
    prompt: str
    style: str = "photorealistic"
    aspect_ratio: str = "16:9"
    quality: str = "standard"


class ContentImagesRequest(BaseModel):
    """Request model for content images generation."""
    content_topic: str
    num_images: int = 3
    content_type: str = "blog_post"


class ImageResponse(BaseModel):
    """Response model for generated image."""
    success: bool
    url: str = None
    prompt: str = None
    style: str = None
    aspect_ratio: str = None
    alt_text: str = None
    error: str = None


class ContentImagesResponse(BaseModel):
    """Response model for content images."""
    success: bool
    images: List[Dict[str, Any]] = []
    count: int = 0
    error: str = None


@app.on_event("startup")
async def startup_event():
    """Initialize the image generator on startup."""
    global image_generator
    try:
        image_generator = create_image_generator()
        logger.info("✅ Image generator initialized successfully")
        logger.info(f"🌐 API Base URL: {settings.image_api_base_url}")
        logger.info(f"🔑 API Key Configured: {'Yes' if settings.image_api_key else 'No'}")
    except Exception as e:
        logger.error(f"❌ Failed to initialize image generator: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global image_generator
    if image_generator:
        await image_generator.close()


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Image Generation API",
        "version": "1.0.0",
        "endpoints": {
            "generate": "/generate - Generate a single image",
            "generate-content": "/generate-content - Generate multiple images for content",
            "styles": "/styles - Get available styles",
            "sizes": "/sizes - Get available aspect ratios",
            "health": "/health - Health check"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    api_configured = bool(settings.image_api_key)
    return {
        "status": "healthy" if api_configured else "warning",
        "api_configured": api_configured,
        "message": "API ready" if api_configured else "API key not configured"
    }


@app.get("/styles")
async def get_styles():
    """Get available image styles."""
    global image_generator
    if not image_generator:
        raise HTTPException(status_code=500, detail="Image generator not initialized")
    
    return {
        "styles": image_generator.AVAILABLE_STYLES,
        "description": "Available image styles for generation"
    }


@app.get("/sizes")
async def get_sizes():
    """Get available aspect ratios."""
    global image_generator
    if not image_generator:
        raise HTTPException(status_code=500, detail="Image generator not initialized")
    
    return {
        "sizes": image_generator.AVAILABLE_SIZES,
        "description": "Available aspect ratios for generation"
    }


@app.post("/generate", response_model=ImageResponse)
async def generate_image(request: ImageRequest):
    """Generate a single image."""
    global image_generator
    
    if not image_generator:
        return ImageResponse(
            success=False,
            error="Image generator not initialized"
        )
    
    try:
        result = await image_generator.generate_image(
            prompt=request.prompt,
            style=request.style,
            aspect_ratio=request.aspect_ratio,
            quality=request.quality
        )
        
        return ImageResponse(
            success=True,
            url=result.url,
            prompt=result.prompt,
            style=result.style,
            aspect_ratio=result.aspect_ratio,
            alt_text=result.alt_text
        )
        
    except Exception as e:
        logger.error(f"Image generation failed: {e}")
        return ImageResponse(
            success=False,
            error=str(e)
        )


@app.post("/generate-content", response_model=ContentImagesResponse)
async def generate_content_images(request: ContentImagesRequest):
    """Generate multiple images for content."""
    global image_generator
    
    if not image_generator:
        return ContentImagesResponse(
            success=False,
            error="Image generator not initialized"
        )
    
    try:
        results = await image_generator.generate_content_images(
            content_topic=request.content_topic,
            content_type=request.content_type,
            num_images=request.num_images
        )
        
        images = []
        for result in results:
            images.append({
                "url": result.url,
                "prompt": result.prompt,
                "style": result.style,
                "aspect_ratio": result.aspect_ratio,
                "alt_text": result.alt_text
            })
        
        return ContentImagesResponse(
            success=True,
            images=images,
            count=len(images)
        )
        
    except Exception as e:
        logger.error(f"Content images generation failed: {e}")
        return ContentImagesResponse(
            success=False,
            error=str(e)
        )


if __name__ == "__main__":
    print("🚀 Starting Image Generation API Server...")
    print("📖 API Documentation: http://localhost:8001/docs")
    print("🔍 Health Check: http://localhost:8001/health")
    print("🎨 Generate Image: POST http://localhost:8001/generate")
    print("📝 Generate Content: POST http://localhost:8001/generate-content")
    print("🎭 Available Styles: GET http://localhost:8001/styles")
    print("📐 Available Sizes: GET http://localhost:8001/sizes")

    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
