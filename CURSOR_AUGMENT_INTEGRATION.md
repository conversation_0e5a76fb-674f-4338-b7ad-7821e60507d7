# 在Cursor和Augment中使用图片生成API

## 🎉 好消息！

您的图片生成API已经完全可用！我们已经成功测试了所有功能：

- ✅ API服务器正常启动
- ✅ 健康检查通过
- ✅ 样式和尺寸查询正常
- ✅ **图片生成成功！** 生成了图片：https://image.apiihub.com/file/image/3/ea93a1a7-44a4-42f5-ae1c-b15631daaa82.png

## 🚀 快速开始

### 方法1：HTTP API服务器（推荐）

1. **启动服务器**：
```bash
cd /Users/<USER>/PycharmProjects/imgMcp
python simple_api_server.py
```

2. **在Cursor中使用**：
告诉Cursor AI助手：
```
我有一个本地图片生成API运行在 http://localhost:8001

可用端点：
- POST /generate - 生成单张图片
- POST /generate-content - 生成内容图片
- GET /styles - 获取可用样式
- GET /sizes - 获取可用尺寸

请求示例：
curl -X POST "http://localhost:8001/generate" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "描述", "style": "photorealistic", "aspect_ratio": "16:9"}'

请帮我使用这个API生成图片。
```

3. **在Augment中使用**：
在Augment中描述：
```
我有一个图片生成API服务：
- 地址：http://localhost:8001
- 生成图片：POST /generate
- 参数：{"prompt": "描述", "style": "样式", "aspect_ratio": "比例"}
- 可用样式：photorealistic, illustration, infographic, diagram, artistic, cartoon
- 可用比例：16:9, 1:1, 9:16, 4:3, 3:4

请使用这个API为我生成图片。
```

### 方法2：命令行工具

1. **在Cursor中使用**：
```
我有一个图片生成命令行工具：

基本用法：
python image_cli.py generate "图片描述" --style photorealistic --aspect-ratio 16:9 --format json

查看选项：
python image_cli.py styles  # 查看可用样式
python image_cli.py sizes   # 查看可用尺寸
python image_cli.py status  # 查看API状态

请帮我使用这个工具生成图片。
```

## 📋 实际使用示例

### 示例1：生成博客头图

**Cursor中的对话**：
```
用户：请帮我为"机器学习入门教程"这篇博客生成一张头图

AI助手：我来帮您使用图片生成API创建博客头图。

[AI会执行以下命令或API调用]
curl -X POST "http://localhost:8001/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Machine learning tutorial hero image, modern technology, data visualization, neural networks",
    "style": "photorealistic", 
    "aspect_ratio": "16:9"
  }'
```

### 示例2：生成多张内容图片

**API调用**：
```bash
curl -X POST "http://localhost:8001/generate-content" \
  -H "Content-Type: application/json" \
  -d '{
    "content_topic": "Python编程教程",
    "num_images": 3,
    "content_type": "blog_post"
  }'
```

### 示例3：命令行快速生成

```bash
# 生成单张图片
python image_cli.py generate "现代办公室工作环境" --style photorealistic --format json

# 生成内容图片
python image_cli.py content "区块链技术解析" --num-images 3 --format json
```

## 🔧 集成到工作流程

### 在Cursor中的工作流程

1. **启动API服务器**（一次性）
2. **告诉Cursor关于API**（每次会话开始时）
3. **正常对话生成图片**

示例对话：
```
用户：我需要为我的技术博客生成一些图片
AI：我可以使用您的图片生成API来帮您创建图片。请告诉我：
1. 博客的主题是什么？
2. 需要什么风格的图片？
3. 需要几张图片？

用户：主题是"Docker容器化技术"，需要专业风格，3张图片
AI：好的，我来为您生成3张关于Docker容器化技术的专业图片...
[执行API调用]
```

### 在Augment中的工作流程

1. **描述API能力**（项目开始时）
2. **在需要时请求生成图片**
3. **AI自动调用API**

## 🎨 可用选项

### 图片样式
- `photorealistic` - 照片级真实
- `illustration` - 插图风格  
- `infographic` - 信息图表
- `diagram` - 图表示意图
- `artistic` - 艺术风格
- `cartoon` - 卡通风格

### 尺寸比例
- `16:9` - 宽屏横向 (1792x1024)
- `1:1` - 正方形 (1024x1024)
- `9:16` - 竖屏 (1024x1792)
- `4:3` - 标准横向 (1536x1152)
- `3:4` - 标准竖向 (1152x1536)

## 🔍 故障排除

### 常见问题

1. **端口被占用**：修改 `simple_api_server.py` 中的端口号
2. **API密钥问题**：检查 `.env` 文件中的 `IMAGE_API_KEY`
3. **依赖缺失**：运行 `pip install fastapi uvicorn requests`

### 测试API状态

```bash
# 快速测试
python test_api.py

# 检查API状态
python image_cli.py status

# 健康检查
curl http://localhost:8001/health
```

## 🎯 推荐使用方式

1. **日常使用**：启动HTTP API服务器，在Cursor/Augment中通过对话使用
2. **批量处理**：使用命令行工具
3. **开发集成**：直接导入Python模块

您的图片生成接口现在完全可以被Cursor和Augment使用了！🎉
