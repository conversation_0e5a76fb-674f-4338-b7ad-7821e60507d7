#!/bin/bash

# Image Generation MCP Server Startup Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Image Generation MCP Server...${NC}"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found. Creating one...${NC}"
    python3 -m venv .venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to create virtual environment${NC}"
        exit 1
    fi
fi

# Activate virtual environment
echo -e "${BLUE}🔧 Activating virtual environment...${NC}"
source .venv/bin/activate

# Install dependencies if requirements.txt has changed
if [ requirements.txt -nt .venv/pyvenv.cfg ] || [ ! -f .venv/lib/python*/site-packages/mcp ]; then
    echo -e "${YELLOW}📦 Installing/updating dependencies...${NC}"
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to install dependencies${NC}"
        exit 1
    fi
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Please create one with your API configuration.${NC}"
    echo -e "${BLUE}💡 You can copy .env.example and modify it:${NC}"
    echo -e "${BLUE}   cp .env.example .env${NC}"
    echo -e "${BLUE}   nano .env${NC}"
    exit 1
fi

# Check if IMAGE_API_KEY is set
if ! grep -q "IMAGE_API_KEY=" .env || grep -q "IMAGE_API_KEY=your_api_key_here" .env; then
    echo -e "${YELLOW}⚠️  IMAGE_API_KEY not properly configured in .env file${NC}"
    echo -e "${BLUE}💡 Please set your APIIHUB API key in the .env file${NC}"
fi

# Start the server
echo -e "${GREEN}✅ Starting MCP Server...${NC}"
echo -e "${BLUE}📍 Server will run on stdio - ready for MCP client connections${NC}"
echo -e "${BLUE}🔗 Configure Claude Desktop to connect to this server${NC}"
echo -e "${YELLOW}📋 Press Ctrl+C to stop the server${NC}"
echo ""

python image_mcp_server.py

echo -e "${BLUE}👋 Server stopped${NC}" 