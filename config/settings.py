"""Configuration settings for the Image MCP Server."""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings."""
    
    # Image API Configuration
    image_api_key: Optional[str] = Field(
        default=None,
        env="IMAGE_API_KEY",
        description="API key for image generation service"
    )
    
    image_api_base_url: str = Field(
        default="https://api.apiihub.com",
        env="IMAGE_API_BASE_URL",
        description="Base URL for image generation API"
    )
    
    # Langsmith Configuration
    langchain_tracing_v2: bool = Field(
        default=False,
        env="LANGCHAIN_TRACING_V2",
        description="Enable Langsmith tracing"
    )
    
    langchain_endpoint: Optional[str] = Field(
        default=None,
        env="LANGCHAIN_ENDPOINT",
        description="Langsmith endpoint URL"
    )
    
    langchain_api_key: Optional[str] = Field(
        default=None,
        env="LANGCHAIN_API_KEY",
        description="Langsmith API key"
    )
    
    langchain_project: str = Field(
        default="image-mcp-server",
        env="LANGCHAIN_PROJECT",
        description="Langsmith project name"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings() 