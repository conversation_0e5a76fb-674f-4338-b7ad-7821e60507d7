#!/usr/bin/env node

/**
 * Test script for the Image Generation MCP Server
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function testMCPServer() {
  console.log('🧪 Testing Image Generation MCP Server...\n');

  const serverPath = join(__dirname, 'index.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseData = '';

  server.stdout.on('data', (data) => {
    responseData += data.toString();
  });

  server.stderr.on('data', (data) => {
    console.log('Server log:', data.toString());
  });

  // Test 1: List tools
  console.log('1. Testing list_tools...');
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list',
    params: {}
  };

  server.stdin.write(JSON.stringify(listToolsRequest) + '\n');

  // Test 2: Get available styles
  setTimeout(() => {
    console.log('2. Testing get_available_styles...');
    const getStylesRequest = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'get_available_styles',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(getStylesRequest) + '\n');
  }, 1000);

  // Test 3: Get available sizes
  setTimeout(() => {
    console.log('3. Testing get_available_sizes...');
    const getSizesRequest = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'get_available_sizes',
        arguments: {}
      }
    };
    server.stdin.write(JSON.stringify(getSizesRequest) + '\n');
  }, 2000);

  // Close and analyze results
  setTimeout(() => {
    server.stdin.end();
    server.kill();
    
    console.log('\n📊 Test Results:');
    
    if (responseData.includes('generate_image')) {
      console.log('✅ Tools listing works');
    } else {
      console.log('❌ Tools listing failed');
    }
    
    if (responseData.includes('photorealistic')) {
      console.log('✅ Styles retrieval works');
    } else {
      console.log('❌ Styles retrieval failed');
    }
    
    if (responseData.includes('16:9')) {
      console.log('✅ Sizes retrieval works');
    } else {
      console.log('❌ Sizes retrieval failed');
    }
    
    console.log('\n🎉 MCP Server test completed!');
    console.log('\nTo use with Augment/Cursor, add this to your MCP configuration:');
    console.log(JSON.stringify({
      mcpServers: {
        "image-generation": {
          command: "node",
          args: [serverPath]
        }
      }
    }, null, 2));
    
  }, 3000);

  server.on('error', (error) => {
    console.error('❌ Server error:', error);
  });
}

testMCPServer();
