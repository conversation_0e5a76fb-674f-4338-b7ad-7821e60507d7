# Image Generation MCP Server

A Model Context Protocol (MCP) server that provides AI-powered image generation capabilities using the APIIHUB image generation API. This server allows AI assistants like <PERSON> to generate images directly through standardized MCP tools.

## Features

- 🎨 **Single Image Generation**: Generate individual images with custom prompts, styles, and aspect ratios
- 📚 **Content Image Batch Generation**: Create multiple themed images for blog posts, articles, and presentations
- 🎭 **Multiple Styles**: Support for photorealistic, illustration, infographic, diagram, artistic, and cartoon styles
- 📐 **Flexible Aspect Ratios**: Various aspect ratios including 16:9, 1:1, 4:3, portrait, and landscape formats
- 🔧 **Resource Management**: Built-in API status checking and configuration validation
- 📋 **Prompt Templates**: Pre-configured prompts for common use cases

## Installation

1. **Clone the repository and navigate to the project directory**:
```bash
cd /path/to/your/project
```

2. **Create and activate a virtual environment**:
```bash
python -m venv .venv
source .venv/bin/activate  # On macOS/Linux
# or
.venv\Scripts\activate     # On Windows
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Configure environment variables**:
Create a `.env` file in the project root:
```bash
# Image Generation API Configuration
IMAGE_API_KEY=your_apiihub_api_key_here
IMAGE_API_BASE_URL=https://api.apiihub.com

# Optional: Langsmith Configuration for tracing
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_key_here
LANGCHAIN_PROJECT=image-mcp-server
```

## Usage

### Running the MCP Server

Start the MCP server:
```bash
source .venv/bin/activate
python image_mcp_server.py
```

The server will start and listen for MCP requests via stdio.

### Connecting to Claude Desktop

Add the following configuration to your Claude Desktop configuration file:

**On macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**On Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "image-generator": {
      "command": "python",
      "args": ["/absolute/path/to/your/project/image_mcp_server.py"],
      "env": {
        "IMAGE_API_KEY": "your_apiihub_api_key_here"
      }
    }
  }
}
```

### Available Tools

#### 1. `generate_image`
Generate a single image based on a text prompt.

**Parameters**:
- `prompt` (required): The text prompt describing the image
- `style` (optional): Image style preference
  - Options: `photorealistic`, `illustration`, `infographic`, `diagram`, `screenshot`, `realistic`, `artistic`, `cartoon`
  - Default: `photorealistic`
- `aspect_ratio` (optional): Image aspect ratio
  - Options: `1:1`, `16:9`, `9:16`, `4:3`, `3:4`, `square`, `landscape`, `portrait`
  - Default: `16:9`
- `quality` (optional): Image quality setting
  - Options: `standard`, `hd`
  - Default: `standard`

**Example**:
```
Generate an image with the prompt "A modern office workspace with natural lighting" in photorealistic style with 16:9 aspect ratio.
```

#### 2. `generate_content_images`
Generate multiple images for content creation based on a topic.

**Parameters**:
- `content_topic` (required): The main topic for content creation
- `content_type` (optional): Type of content being created
  - Options: `blog_post`, `article`, `social_media`, `presentation`
  - Default: `blog_post`
- `num_images` (optional): Number of images to generate (1-5)
  - Default: `3`

**Example**:
```
Generate 3 images for a blog post about "sustainable energy solutions".
```

#### 3. `get_available_styles`
Get a list of all available image styles and their descriptions.

#### 4. `get_available_sizes`
Get a list of all available aspect ratios and their dimensions.

### Available Prompts

The server provides pre-configured prompts for common scenarios:

#### 1. `blog_hero_image`
Generate a hero image for a blog post.
- `topic` (required): The main topic of the blog post
- `style` (optional): Visual style preference

#### 2. `social_media_post`
Generate an image for social media content.
- `message` (required): The main message or theme
- `platform` (optional): Social media platform (`instagram`, `twitter`, `linkedin`)

#### 3. `technical_diagram`
Generate a technical diagram or illustration.
- `concept` (required): The technical concept to illustrate
- `complexity` (optional): Complexity level (`simple`, `medium`, `detailed`)

### Available Resources

The server exposes configuration and status information through resources:

- `config://image-styles`: List of available image styles
- `config://image-sizes`: List of available aspect ratios and dimensions
- `config://api-status`: Current status of the image generation API

## API Configuration

This server uses the APIIHUB image generation API. You'll need:

1. **API Key**: Sign up at [APIIHUB](https://apiihub.com) to get your API key
2. **Base URL**: The default base URL is `https://api.apiihub.com`

Set these in your `.env` file or environment variables.

## Error Handling

The server includes comprehensive error handling:

- **Missing API Key**: Warning logged, but server continues to run
- **API Failures**: Detailed error messages returned to the client
- **Invalid Parameters**: Clear validation error messages
- **Network Issues**: Timeout and connection error handling

## Logging

The server logs important events including:
- Server startup and configuration status
- Image generation requests and results
- API errors and performance metrics
- Resource cleanup on shutdown

## Development

### Project Structure
```
.
├── config/
│   ├── __init__.py
│   └── settings.py          # Configuration management
├── tools/
│   ├── __init__.py
│   └── image_generator.py   # Core image generation logic
├── image_mcp_server.py      # Main MCP server implementation
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
└── README.md               # This file
```

### Adding New Features

To add new image generation features:

1. **Extend the ImageGeneratorTool** in `tools/image_generator.py`
2. **Add new MCP tools** in `image_mcp_server.py`
3. **Update the tool schemas** in the `handle_list_tools()` function
4. **Test the new functionality** with Claude Desktop

## Troubleshooting

### Common Issues

1. **"IMAGE_API_KEY not configured"**
   - Make sure your `.env` file contains the correct API key
   - Verify the API key is valid and active

2. **"Image generation failed: HTTP 401"**
   - Check that your API key is correct and hasn't expired
   - Verify you have sufficient API credits

3. **"Connection timeout"**
   - Check your internet connection
   - Verify the API base URL is correct

4. **MCP Server not appearing in Claude**
   - Verify the path in `claude_desktop_config.json` is absolute and correct
   - Check that Python can be found in your system PATH
   - Restart Claude Desktop after configuration changes

### Debug Mode

To run with detailed logging:
```bash
LOGLEVEL=DEBUG python image_mcp_server.py
```

## License

This project is open source. Please check the APIIHUB terms of service for API usage requirements.

## Support

For issues related to:
- **MCP Server**: Open an issue in this repository
- **APIIHUB API**: Contact APIIHUB support
- **Claude Desktop Integration**: Check Claude's MCP documentation 