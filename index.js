#!/usr/bin/env node

/**
 * Image Generation MCP Server
 * 
 * A Model Context Protocol server that provides image generation capabilities
 * using the APIIHUB image generation API.
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ListResourcesRequestSchema,
  ReadResourceRequestSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const API_BASE_URL = process.env.IMAGE_API_BASE_URL || 'https://api.apiihub.com';
const API_KEY = "sk-duIFzjkW39xuKs0IwRkdkVttlyfyHDEW";

// Available styles and sizes
const AVAILABLE_STYLES = {
  'photorealistic': 'realistic',
  'illustration': 'illustration',
  'infographic': 'infographic',
  'diagram': 'diagram',
  'screenshot': 'screenshot',
  'realistic': 'realistic',
  'artistic': 'artistic',
  'cartoon': 'cartoon'
};

const AVAILABLE_SIZES = {
  '1:1': '1024x1024',
  '16:9': '1792x1024',
  '9:16': '1024x1792',
  '4:3': '1536x1152',
  '3:4': '1152x1536',
  'square': '1024x1024',
  'landscape': '1792x1024',
  'portrait': '1024x1792'
};

// Create server instance
const server = new Server(
  {
    name: 'image-generation-mcp',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
      resources: {},
      prompts: {},
    },
  }
);

// Helper function to enhance prompts for SEO
function enhancePromptForSEO(prompt, style) {
  const styleModifiers = {
    'photorealistic': 'high-quality, professional, photorealistic',
    'illustration': 'clean illustration, modern design, professional',
    'infographic': 'clean infographic style, data visualization, professional layout',
    'diagram': 'clear diagram, educational, well-organized layout',
    'screenshot': 'clean interface, modern UI design, professional'
  };
  
  const modifier = styleModifiers[style] || 'high-quality, professional';
  return `${prompt}, ${modifier}, web-friendly, engaging, high resolution, suitable for blog content`;
}

// Helper function to generate alt text
function generateAltText(prompt, style) {
  const words = prompt.toLowerCase().split();
  const descriptiveWords = words.filter(word => 
    !['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'].includes(word) &&
    word.length > 2
  );
  
  return `${style} image showing ${descriptiveWords.slice(0, 5).join(' ')}`;
}

// Helper function to generate image
async function generateImage(prompt, style = 'photorealistic', aspectRatio = '16:9') {
  if (!API_KEY) {
    throw new Error('IMAGE_API_KEY not configured');
  }

  const apiStyle = AVAILABLE_STYLES[style] || 'realistic';
  const size = AVAILABLE_SIZES[aspectRatio] || '1792x1024';
  const enhancedPrompt = enhancePromptForSEO(prompt, style);

  try {
    const response = await axios.post(
      `${API_BASE_URL}/api/v1/ai-tools/execute/text-to-image`,
      {
        text: enhancedPrompt,
        style: apiStyle,
        size: size
      },
      {
        headers: {
          'x-api-key': API_KEY,
          'Content-Type': 'application/json'
        },
        timeout: 120000 // 2 minutes timeout
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.error || 'Unknown error from image API');
    }

    const data = response.data.data;
    if (!data.image_url) {
      throw new Error('No image URL returned from API');
    }

    return {
      url: data.image_url,
      prompt: enhancedPrompt,
      style: style,
      aspectRatio: aspectRatio,
      altText: generateAltText(prompt, style),
      model: data.model || 'dall-e-3',
      executionTime: data.execution_time || 0
    };

  } catch (error) {
    if (error.response) {
      throw new Error(`Image generation failed: HTTP ${error.response.status}`);
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Image generation timed out');
    } else {
      throw new Error(`Image generation failed: ${error.message}`);
    }
  }
}

// List available tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'generate_image',
        description: 'Generate a single image based on a text prompt',
        inputSchema: {
          type: 'object',
          properties: {
            prompt: {
              type: 'string',
              description: 'The text prompt describing the image to generate'
            },
            style: {
              type: 'string',
              description: 'Image style preference',
              enum: Object.keys(AVAILABLE_STYLES),
              default: 'photorealistic'
            },
            aspect_ratio: {
              type: 'string',
              description: 'Image aspect ratio',
              enum: Object.keys(AVAILABLE_SIZES),
              default: '16:9'
            }
          },
          required: ['prompt']
        }
      },
      {
        name: 'generate_content_images',
        description: 'Generate multiple images for content creation',
        inputSchema: {
          type: 'object',
          properties: {
            content_topic: {
              type: 'string',
              description: 'The main topic for content creation'
            },
            num_images: {
              type: 'integer',
              description: 'Number of images to generate (1-5)',
              minimum: 1,
              maximum: 5,
              default: 3
            }
          },
          required: ['content_topic']
        }
      },
      {
        name: 'get_available_styles',
        description: 'Get list of available image styles',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'get_available_sizes',
        description: 'Get list of available image aspect ratios',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      }
    ]
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case 'generate_image': {
        const { prompt, style = 'photorealistic', aspect_ratio = '16:9' } = args;
        
        if (!prompt) {
          throw new Error('Prompt is required');
        }

        const result = await generateImage(prompt, style, aspect_ratio);

        return {
          content: [
            {
              type: 'text',
              text: `✅ Image generated successfully!\n\n` +
                    `**Image URL:** ${result.url}\n` +
                    `**Prompt:** ${result.prompt}\n` +
                    `**Style:** ${result.style}\n` +
                    `**Aspect Ratio:** ${result.aspectRatio}\n` +
                    `**Alt Text:** ${result.altText}\n\n` +
                    `You can use this image in your content by copying the URL above.`
            },
            {
              type: 'image',
              data: result.url,
              mimeType: 'image/png'
            }
          ]
        };
      }

      case 'generate_content_images': {
        const { content_topic, num_images = 3 } = args;
        
        if (!content_topic) {
          throw new Error('Content topic is required');
        }

        // Create diverse prompts for content
        const prompts = [
          { prompt: `Hero image for ${content_topic}`, style: 'photorealistic', aspect_ratio: '16:9' },
          { prompt: `Infographic elements for ${content_topic}`, style: 'infographic', aspect_ratio: '4:3' },
          { prompt: `Step-by-step illustration for ${content_topic}`, style: 'illustration', aspect_ratio: '16:9' },
          { prompt: `Statistical chart visualization for ${content_topic}`, style: 'diagram', aspect_ratio: '4:3' },
          { prompt: `Professional example of ${content_topic}`, style: 'photorealistic', aspect_ratio: '3:4' }
        ].slice(0, num_images);

        const results = await Promise.allSettled(
          prompts.map(p => generateImage(p.prompt, p.style, p.aspect_ratio))
        );

        const successfulImages = results
          .filter(result => result.status === 'fulfilled')
          .map(result => result.value);

        if (successfulImages.length === 0) {
          return {
            content: [{
              type: 'text',
              text: '❌ Failed to generate images. Please check your API configuration.'
            }]
          };
        }

        const content = [
          {
            type: 'text',
            text: `✅ Generated ${successfulImages.length} images for '${content_topic}'\n\n`
          }
        ];

        successfulImages.forEach((result, i) => {
          content.push({
            type: 'text',
            text: `**Image ${i + 1}:**\n` +
                  `- URL: ${result.url}\n` +
                  `- Prompt: ${result.prompt}\n` +
                  `- Style: ${result.style}\n` +
                  `- Aspect Ratio: ${result.aspectRatio}\n` +
                  `- Alt Text: ${result.altText}\n\n`
          });
          
          content.push({
            type: 'image',
            data: result.url,
            mimeType: 'image/png'
          });
        });

        return { content };
      }

      case 'get_available_styles': {
        const stylesInfo = Object.entries(AVAILABLE_STYLES)
          .map(([style, apiStyle]) => `- **${style}**: ${apiStyle}`)
          .join('\n');

        return {
          content: [{
            type: 'text',
            text: `📎 **Available Image Styles:**\n\n${stylesInfo}`
          }]
        };
      }

      case 'get_available_sizes': {
        const sizesInfo = Object.entries(AVAILABLE_SIZES)
          .map(([ratio, dimensions]) => `- **${ratio}**: ${dimensions}`)
          .join('\n');

        return {
          content: [{
            type: 'text',
            text: `📐 **Available Aspect Ratios:**\n\n${sizesInfo}`
          }]
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    return {
      content: [{
        type: 'text',
        text: `❌ Error: ${error.message}`
      }]
    };
  }
});

// Start the server
async function main() {
  console.error('🚀 Starting Image Generation MCP Server...');
  console.error(`🌐 API Base URL: ${API_BASE_URL}`);
  console.error(`🔑 API Key Configured: ${API_KEY ? 'Yes' : 'No'}`);
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('✅ Image Generation MCP Server started successfully');
}

main().catch((error) => {
  console.error('❌ Server failed to start:', error);
  process.exit(1);
});
