#!/bin/bash

echo "🚀 Setting up Image Generation MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm found: $(npm --version)"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Copy .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file and add your IMAGE_API_KEY"
else
    echo "✅ .env file already exists"
fi

# Make the script executable
chmod +x index.js

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your IMAGE_API_KEY"
echo "2. Test the server: npm start"
echo "3. Add to Augment/Cursor MCP configuration"
echo ""
echo "MCP Configuration:"
echo '{'
echo '  "mcpServers": {'
echo '    "image-generation": {'
echo '      "command": "node",'
echo '      "args": ["'$(pwd)'/index.js"]'
echo '    }'
echo '  }'
echo '}'
