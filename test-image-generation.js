#!/usr/bin/env node

/**
 * Test image generation functionality
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function testImageGeneration() {
  console.log('🎨 Testing Image Generation...\n');

  const serverPath = join(__dirname, 'index.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  let responseData = '';

  server.stdout.on('data', (data) => {
    responseData += data.toString();
  });

  server.stderr.on('data', (data) => {
    console.log('Server log:', data.toString());
  });

  // Test image generation
  setTimeout(() => {
    console.log('🖼️ Testing image generation...');
    const generateImageRequest = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: 'generate_image',
        arguments: {
          prompt: 'A beautiful sunset over mountains',
          style: 'photorealistic',
          aspect_ratio: '16:9'
        }
      }
    };
    
    server.stdin.write(JSON.stringify(generateImageRequest) + '\n');
  }, 1000);

  // Close and analyze results
  setTimeout(() => {
    server.stdin.end();
    server.kill();
    
    console.log('\n📊 Image Generation Test Results:');
    
    if (responseData.includes('Image generated successfully')) {
      console.log('✅ Image generation works!');
      
      // Try to extract the image URL
      const urlMatch = responseData.match(/https:\/\/[^\s\n]+\.png/);
      if (urlMatch) {
        console.log(`🔗 Generated image URL: ${urlMatch[0]}`);
      }
    } else if (responseData.includes('Error')) {
      console.log('❌ Image generation failed');
      console.log('Error details:', responseData);
    } else {
      console.log('⏳ Image generation may still be in progress or response not captured');
    }
    
    console.log('\n🎉 Test completed!');
    
  }, 30000); // Wait 30 seconds for image generation

  server.on('error', (error) => {
    console.error('❌ Server error:', error);
  });
}

console.log('⚠️  This test will make a real API call and may take up to 30 seconds.');
console.log('Press Ctrl+C to cancel, or wait to continue...\n');

setTimeout(() => {
  testImageGeneration();
}, 3000);
