#!/usr/bin/env python3
"""
Test script for Image Generation MCP Server

This script allows you to test the image generation functionality
before setting up the full MCP server integration.
"""

import asyncio
import os
from tools.image_generator import create_image_generator


async def test_image_generation():
    """Test the image generation functionality."""
    print("🧪 Testing Image Generation Tool...")
    
    # Create image generator
    generator = create_image_generator()
    
    try:
        # Test single image generation
        print("\n🎨 Testing single image generation...")
        result = await generator.generate_image(
            prompt="A beautiful sunset over mountains",
            style="photorealistic",
            aspect_ratio="16:9"
        )
        
        print(f"✅ Image generated successfully!")
        print(f"📷 URL: {result.url}")
        print(f"🏷️  Alt text: {result.alt_text}")
        print(f"🎭 Style: {result.style}")
        print(f"📐 Aspect ratio: {result.aspect_ratio}")
        
        # Test batch generation
        print("\n📚 Testing batch image generation...")
        results = await generator.generate_content_images(
            content_topic="sustainable technology",
            content_type="blog_post",
            num_images=2
        )
        
        print(f"✅ Generated {len(results)} images for content!")
        for i, img in enumerate(results, 1):
            print(f"  {i}. {img.url}")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
    
    finally:
        await generator.close()


def main():
    """Main function to run tests."""
    print("🚀 Image Generation MCP Server Test")
    print("=" * 50)
    
    # Check if API key is configured
    from config.settings import settings
    
    if not settings.image_api_key:
        print("⚠️  WARNING: IMAGE_API_KEY not configured!")
        print("📝 Please create a .env file with your API configuration.")
        print("💡 You can copy env_example.txt to .env and modify it.")
        return
    
    print(f"🔑 API Key: {'*' * (len(settings.image_api_key) - 4) + settings.image_api_key[-4:]}")
    print(f"🌐 API Base URL: {settings.image_api_base_url}")
    
    # Run async test
    asyncio.run(test_image_generation())
    
    print("\n🎉 Testing completed!")
    print("💡 If tests passed, you can now run the MCP server with:")
    print("   python image_mcp_server.py")


if __name__ == "__main__":
    main()

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
