{"name": "image-generation-mcp", "version": "1.0.0", "description": "MCP server for image generation using APIIHUB", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node --watch index.js"}, "keywords": ["mcp", "image-generation", "ai", "<PERSON><PERSON><PERSON><PERSON>"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0", "axios": "^1.6.0", "dotenv": "^16.3.0"}, "bin": {"image-generation-mcp": "./index.js"}}