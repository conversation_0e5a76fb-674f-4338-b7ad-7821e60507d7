<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="1" class="java.lang.String" itemvalue="asyncio" />
            <item index="2" class="java.lang.String" itemvalue="pytest" />
            <item index="3" class="java.lang.String" itemvalue="mypy" />
            <item index="4" class="java.lang.String" itemvalue="black" />
            <item index="5" class="java.lang.String" itemvalue="isort" />
            <item index="6" class="java.lang.String" itemvalue="pytest-asyncio" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>