#!/usr/bin/env python3
"""
测试图片生成API的简单脚本
"""

import requests
import json

def test_api():
    """测试API端点"""
    base_url = "http://localhost:8001"
    
    print("🧪 Testing Image Generation API...")
    
    # 1. 测试健康检查
    print("\n1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 2. 测试获取样式
    print("\n2. 获取可用样式...")
    try:
        response = requests.get(f"{base_url}/styles")
        print(f"   状态码: {response.status_code}")
        data = response.json()
        print(f"   可用样式: {list(data['styles'].keys())}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 3. 测试获取尺寸
    print("\n3. 获取可用尺寸...")
    try:
        response = requests.get(f"{base_url}/sizes")
        print(f"   状态码: {response.status_code}")
        data = response.json()
        print(f"   可用尺寸: {list(data['sizes'].keys())}")
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 4. 测试图片生成（这个会调用真实API，可能需要API密钥）
    print("\n4. 测试图片生成...")
    try:
        payload = {
            "prompt": "A beautiful sunset over mountains",
            "style": "photorealistic",
            "aspect_ratio": "16:9"
        }
        
        print(f"   请求: {json.dumps(payload, indent=2)}")
        response = requests.post(
            f"{base_url}/generate",
            json=payload,
            timeout=30  # 图片生成可能需要较长时间
        )
        
        print(f"   状态码: {response.status_code}")
        result = response.json()
        
        if result.get("success"):
            print(f"   ✅ 成功生成图片!")
            print(f"   图片URL: {result.get('url')}")
            print(f"   Alt文本: {result.get('alt_text')}")
        else:
            print(f"   ❌ 生成失败: {result.get('error')}")
            
    except requests.exceptions.Timeout:
        print("   ⏰ 请求超时（这是正常的，图片生成需要时间）")
    except Exception as e:
        print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    test_api()
