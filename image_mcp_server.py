#!/usr/bin/env python3
"""
Image Generation MCP Server

A Model Context Protocol server that provides image generation capabilities
using the APIIHUB image generation API.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Sequence

from mcp.server import Server
from mcp.types import (
    Resource, 
    Tool, 
    TextContent, 
    ImageContent,
    CallToolResult,
    ListToolsResult,
    ListResourcesResult,
    ReadResourceResult,
    GetPromptResult,
    ListPromptsResult,
    Prompt,
    PromptMessage,
    PromptArgument
)
from pydantic import AnyUrl

from tools.image_generator import create_image_generator, ImageGeneratorTool, GeneratedImage
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("image-mcp-server")

# Create server instance
server = Server("image-mcp-server")

# Global image generator instance
image_generator: Optional[ImageGeneratorTool] = None


@server.list_tools()
async def handle_list_tools() -> dict:
    """List available tools for image generation."""
    logger.info("🔧 list_tools() called")
    
    try:
        tools = [
            Tool(
                name="generate_image",
                description="Generate a single image based on a text prompt",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "prompt": {
                            "type": "string",
                            "description": "The text prompt describing the image to generate"
                        },
                        "style": {
                            "type": "string",
                            "description": "Image style preference (photorealistic, illustration, artistic, cartoon)",
                            "default": "photorealistic"
                        },
                        "aspect_ratio": {
                            "type": "string", 
                            "description": "Image aspect ratio (16:9, 1:1, 9:16, 4:3, 3:4)",
                            "default": "16:9"
                        }
                    },
                    "required": ["prompt"]
                }
            ),
            Tool(
                name="generate_content_images", 
                description="Generate multiple images for content creation",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "content_topic": {
                            "type": "string",
                            "description": "The main topic for content creation"
                        },
                        "num_images": {
                            "type": "integer",
                            "description": "Number of images to generate (1-5)",
                            "default": 3
                        }
                    },
                    "required": ["content_topic"]
                }
            ),
            Tool(
                name="get_available_styles",
                description="Get list of available image styles",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            ),
            Tool(
                name="get_available_sizes", 
                description="Get list of available image aspect ratios",
                inputSchema={
                    "type": "object",
                    "properties": {}
                }
            )
        ]
        
        logger.info(f"✅ Returning {len(tools)} tools")
        for tool in tools:
            logger.info(f"  - {tool.name}: {tool.description}")
        
        # Try returning as dict for MCP 1.0.0 compatibility
        return {
            "tools": tools,
            "nextCursor": None
        }
        
    except Exception as e:
        logger.error(f"❌ Error in list_tools: {e}")
        import traceback
        logger.error(traceback.format_exc())
        # Return empty tools list instead of crashing
        # Try returning as dict for MCP 1.0.0 compatibility
        return {
            "tools": [],
            "nextCursor": None
        }


@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
    """Handle tool calls for image generation."""
    global image_generator
    
    # Initialize image generator if not already done
    if image_generator is None:
        image_generator = create_image_generator()
    
    try:
        if name == "generate_image":
            # Extract parameters
            prompt = arguments.get("prompt")
            if not prompt:
                raise ValueError("Prompt is required")
            
            style = arguments.get("style", "photorealistic")
            aspect_ratio = arguments.get("aspect_ratio", "16:9")
            
            # Generate image
            result = await image_generator.generate_image(
                prompt=prompt,
                style=style,
                aspect_ratio=aspect_ratio,
                quality="standard"
            )
            
            # Return result
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text=f"✅ Image generated successfully!\n\n"
                             f"**Image URL:** {result.url}\n"
                             f"**Prompt:** {result.prompt}\n"
                             f"**Style:** {result.style}\n"
                             f"**Aspect Ratio:** {result.aspect_ratio}\n"
                             f"**Alt Text:** {result.alt_text}\n\n"
                             f"You can use this image in your content by copying the URL above."
                    ),
                    ImageContent(
                        type="image",
                        data=result.url,
                        mimeType="image/png"
                    )
                ]
            )
            
        elif name == "generate_content_images":
            # Extract parameters
            content_topic = arguments.get("content_topic")
            if not content_topic:
                raise ValueError("Content topic is required")
            
            num_images = arguments.get("num_images", 3)
            
            # Generate multiple images
            results = await image_generator.generate_content_images(
                content_topic=content_topic,
                content_type="blog_post",
                num_images=num_images
            )
            
            if not results:
                return CallToolResult(
                    content=[
                        TextContent(
                            type="text",
                            text="❌ Failed to generate images. Please check your API configuration."
                        )
                    ]
                )
            
            # Format results
            content_parts = [
                TextContent(
                    type="text",
                    text=f"✅ Generated {len(results)} images for '{content_topic}'\n\n"
                )
            ]
            
            for i, result in enumerate(results, 1):
                content_parts.append(
                    TextContent(
                        type="text",
                        text=f"**Image {i}:**\n"
                             f"- URL: {result.url}\n"
                             f"- Prompt: {result.prompt}\n"
                             f"- Style: {result.style}\n"
                             f"- Aspect Ratio: {result.aspect_ratio}\n"
                             f"- Alt Text: {result.alt_text}\n\n"
                    )
                )
                
                content_parts.append(
                    ImageContent(
                        type="image",
                        data=result.url,
                        mimeType="image/png"
                    )
                )
            
            return CallToolResult(content=content_parts)
            
        elif name == "get_available_styles":
            styles_info = []
            for style, api_style in image_generator.AVAILABLE_STYLES.items():
                styles_info.append(f"- **{style}**: {api_style}")
            
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text="📎 **Available Image Styles:**\n\n" + "\n".join(styles_info)
                    )
                ]
            )
            
        elif name == "get_available_sizes":
            sizes_info = []
            for ratio, dimensions in image_generator.AVAILABLE_SIZES.items():
                sizes_info.append(f"- **{ratio}**: {dimensions}")
            
            return CallToolResult(
                content=[
                    TextContent(
                        type="text",
                        text="📐 **Available Aspect Ratios:**\n\n" + "\n".join(sizes_info)
                    )
                ]
            )
            
        else:
            raise ValueError(f"Unknown tool: {name}")
            
    except Exception as e:
        logger.error(f"Error in tool call {name}: {str(e)}")
        return CallToolResult(
            content=[
                TextContent(
                    type="text",
                    text=f"❌ Error: {str(e)}"
                )
            ]
        )


@server.list_prompts()
async def handle_list_prompts() -> ListPromptsResult:
    """List available prompts for common image generation scenarios."""
    return ListPromptsResult(
        prompts=[
            Prompt(
                name="blog_hero_image",
                description="Generate a hero image for a blog post",
                arguments=[
                    PromptArgument(
                        name="topic",
                        description="The main topic of the blog post",
                        required=True
                    ),
                    PromptArgument(
                        name="style",
                        description="Visual style preference",
                        required=False
                    )
                ]
            ),
            Prompt(
                name="social_media_post",
                description="Generate an image for social media content",
                arguments=[
                    PromptArgument(
                        name="message",
                        description="The main message or theme",
                        required=True
                    ),
                    PromptArgument(
                        name="platform",
                        description="Social media platform (instagram, twitter, linkedin)",
                        required=False
                    )
                ]
            ),
            Prompt(
                name="technical_diagram",
                description="Generate a technical diagram or illustration",
                arguments=[
                    PromptArgument(
                        name="concept",
                        description="The technical concept to illustrate",
                        required=True
                    ),
                    PromptArgument(
                        name="complexity",
                        description="Complexity level (simple, medium, detailed)",
                        required=False
                    )
                ]
            )
        ]
    )


@server.get_prompt()
async def handle_get_prompt(name: str, arguments: Dict[str, str]) -> GetPromptResult:
    """Handle prompt requests for image generation."""
    
    if name == "blog_hero_image":
        topic = arguments.get("topic", "")
        style = arguments.get("style", "photorealistic")
        
        prompt_text = f"Create a compelling hero image for a blog post about {topic}. The image should be {style}, professional, and engaging for readers."
        
        return GetPromptResult(
            description=f"Hero image prompt for blog post about {topic}",
            messages=[
                PromptMessage(
                    role="user",
                    content=TextContent(
                        type="text",
                        text=f"Please generate an image with the following details:\n"
                             f"- Topic: {topic}\n"
                             f"- Style: {style}\n"
                             f"- Aspect Ratio: 16:9 (landscape)\n"
                             f"- Prompt: {prompt_text}"
                    )
                )
            ]
        )
        
    elif name == "social_media_post":
        message = arguments.get("message", "")
        platform = arguments.get("platform", "instagram")
        
        aspect_ratio = "1:1" if platform == "instagram" else "16:9"
        prompt_text = f"Create an eye-catching social media image for {platform} about {message}. Modern, vibrant, and engaging design."
        
        return GetPromptResult(
            description=f"Social media image for {platform} about {message}",
            messages=[
                PromptMessage(
                    role="user",
                    content=TextContent(
                        type="text",
                        text=f"Please generate an image with the following details:\n"
                             f"- Message: {message}\n"
                             f"- Platform: {platform}\n"
                             f"- Aspect Ratio: {aspect_ratio}\n"
                             f"- Prompt: {prompt_text}"
                    )
                )
            ]
        )
        
    elif name == "technical_diagram":
        concept = arguments.get("concept", "")
        complexity = arguments.get("complexity", "medium")
        
        prompt_text = f"Create a clear technical diagram illustrating {concept}. The diagram should be {complexity} in detail, educational, and easy to understand."
        
        return GetPromptResult(
            description=f"Technical diagram for {concept}",
            messages=[
                PromptMessage(
                    role="user",
                    content=TextContent(
                        type="text",
                        text=f"Please generate an image with the following details:\n"
                             f"- Concept: {concept}\n"
                             f"- Complexity: {complexity}\n"
                             f"- Style: diagram\n"
                             f"- Aspect Ratio: 4:3\n"
                             f"- Prompt: {prompt_text}"
                    )
                )
            ]
        )
        
    else:
        raise ValueError(f"Unknown prompt: {name}")


@server.list_resources()
async def handle_list_resources() -> ListResourcesResult:
    """List available resources."""
    return ListResourcesResult(
        resources=[
            Resource(
                uri=AnyUrl("config://image-styles"),
                name="Available Image Styles",
                description="List of all available image styles and their descriptions"
            ),
            Resource(
                uri=AnyUrl("config://image-sizes"),
                name="Available Image Sizes",
                description="List of all available aspect ratios and their dimensions"
            ),
            Resource(
                uri=AnyUrl("config://api-status"),
                name="API Status",
                description="Current status of the image generation API"
            )
        ]
    )


@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> ReadResourceResult:
    """Handle resource reading requests."""
    global image_generator
    uri_str = str(uri)
    
    if uri_str == "config://image-styles":
        if image_generator is None:
            image_generator = create_image_generator()
        
        styles_content = "# Available Image Styles\n\n"
        for style, api_style in image_generator.AVAILABLE_STYLES.items():
            styles_content += f"- **{style}**: {api_style}\n"
        
        return ReadResourceResult(
            contents=[
                TextContent(
                    type="text",
                    text=styles_content
                )
            ]
        )
        
    elif uri_str == "config://image-sizes":
        if image_generator is None:
            image_generator = create_image_generator()
        
        sizes_content = "# Available Image Sizes\n\n"
        for ratio, dimensions in image_generator.AVAILABLE_SIZES.items():
            sizes_content += f"- **{ratio}**: {dimensions}\n"
        
        return ReadResourceResult(
            contents=[
                TextContent(
                    type="text",
                    text=sizes_content
                )
            ]
        )
        
    elif uri_str == "config://api-status":
        api_configured = bool(settings.image_api_key)
        status_content = f"# API Status\n\n"
        status_content += f"- **API Key Configured**: {'✅ Yes' if api_configured else '❌ No'}\n"
        status_content += f"- **API Base URL**: {settings.image_api_base_url}\n"
        status_content += f"- **Status**: {'🟢 Ready' if api_configured else '🔴 Not Configured'}\n"
        
        return ReadResourceResult(
            contents=[
                TextContent(
                    type="text",
                    text=status_content
                )
            ]
        )
        
    else:
        raise ValueError(f"Unknown resource: {uri}")


async def cleanup():
    """Cleanup resources when shutting down."""
    global image_generator
    if image_generator:
        await image_generator.close()


async def main():
    """Main entry point for the MCP server."""
    try:
        # Validate configuration
        logger.info("🚀 Starting Image Generation MCP Server...")
        logger.info(f"Server name: {server.name}")
        
        try:
            if not settings.image_api_key:
                logger.warning("⚠️ IMAGE_API_KEY not configured. Some features may not work.")
            
            logger.info(f"🌐 API Base URL: {settings.image_api_base_url}")
            logger.info(f"🔑 API Key Configured: {'Yes' if settings.image_api_key else 'No'}")
        except Exception as e:
            logger.warning(f"⚠️ Settings configuration issue: {e}")
        
        # Test tools before starting server
        logger.info("🧪 Testing tools registration...")
        try:
            test_result = await handle_list_tools()
            tools_count = len(test_result["tools"]) if isinstance(test_result, dict) else len(test_result.tools)
            logger.info(f"✅ Tools test passed: {tools_count} tools available")
        except Exception as e:
            logger.error(f"❌ Tools test failed: {e}")
            return
        
        # Import and run the server
        from mcp.server.stdio import stdio_server
        
        logger.info("📡 Setting up stdio server...")
        async with stdio_server() as (read_stream, write_stream):
            logger.info("🔗 Server connected, starting run loop...")
            await server.run(
                read_stream,
                write_stream,
                server.create_initialization_options()
            )
    except KeyboardInterrupt:
        logger.info("⏹️ Shutting down...")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        try:
            await cleanup()
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
        logger.info("👋 Server stopped")


if __name__ == "__main__":
    asyncio.run(main()) 