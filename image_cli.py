#!/usr/bin/env python3
"""
Command Line Interface for Image Generation
可以在终端中直接使用，Cursor和Augment可以通过命令行调用
"""

import asyncio
import argparse
import json
import sys
from typing import List, Dict, Any

from tools.image_generator import create_image_generator
from config.settings import settings


async def generate_single_image(prompt: str, style: str = "photorealistic", 
                               aspect_ratio: str = "16:9", output_format: str = "json"):
    """Generate a single image and output result."""
    try:
        generator = create_image_generator()
        
        result = await generator.generate_image(
            prompt=prompt,
            style=style,
            aspect_ratio=aspect_ratio
        )
        
        if output_format == "json":
            output = {
                "success": True,
                "url": result.url,
                "prompt": result.prompt,
                "style": result.style,
                "aspect_ratio": result.aspect_ratio,
                "alt_text": result.alt_text
            }
            print(json.dumps(output, indent=2, ensure_ascii=False))
        else:
            print(f"✅ Image generated successfully!")
            print(f"🔗 URL: {result.url}")
            print(f"📝 Prompt: {result.prompt}")
            print(f"🎨 Style: {result.style}")
            print(f"📐 Aspect Ratio: {result.aspect_ratio}")
            print(f"🏷️ Alt Text: {result.alt_text}")
        
        await generator.close()
        return True
        
    except Exception as e:
        if output_format == "json":
            error_output = {
                "success": False,
                "error": str(e)
            }
            print(json.dumps(error_output, indent=2))
        else:
            print(f"❌ Error: {e}")
        return False


async def generate_content_images(topic: str, num_images: int = 3, 
                                content_type: str = "blog_post", output_format: str = "json"):
    """Generate multiple images for content."""
    try:
        generator = create_image_generator()
        
        results = await generator.generate_content_images(
            content_topic=topic,
            content_type=content_type,
            num_images=num_images
        )
        
        if output_format == "json":
            images = []
            for result in results:
                images.append({
                    "url": result.url,
                    "prompt": result.prompt,
                    "style": result.style,
                    "aspect_ratio": result.aspect_ratio,
                    "alt_text": result.alt_text
                })
            
            output = {
                "success": True,
                "count": len(images),
                "images": images
            }
            print(json.dumps(output, indent=2, ensure_ascii=False))
        else:
            print(f"✅ Generated {len(results)} images for '{topic}'")
            for i, result in enumerate(results, 1):
                print(f"\n📸 Image {i}:")
                print(f"  🔗 URL: {result.url}")
                print(f"  📝 Prompt: {result.prompt}")
                print(f"  🎨 Style: {result.style}")
                print(f"  📐 Aspect Ratio: {result.aspect_ratio}")
                print(f"  🏷️ Alt Text: {result.alt_text}")
        
        await generator.close()
        return True
        
    except Exception as e:
        if output_format == "json":
            error_output = {
                "success": False,
                "error": str(e)
            }
            print(json.dumps(error_output, indent=2))
        else:
            print(f"❌ Error: {e}")
        return False


def show_styles():
    """Show available styles."""
    generator = create_image_generator()
    styles = generator.AVAILABLE_STYLES
    
    print("🎨 Available Styles:")
    for style, api_style in styles.items():
        print(f"  • {style} -> {api_style}")


def show_sizes():
    """Show available aspect ratios."""
    generator = create_image_generator()
    sizes = generator.AVAILABLE_SIZES
    
    print("📐 Available Aspect Ratios:")
    for ratio, dimensions in sizes.items():
        print(f"  • {ratio} -> {dimensions}")


def show_status():
    """Show API configuration status."""
    api_configured = bool(settings.image_api_key)
    
    print("🔍 API Status:")
    print(f"  • API Key Configured: {'✅ Yes' if api_configured else '❌ No'}")
    print(f"  • API Base URL: {settings.image_api_base_url}")
    print(f"  • Status: {'🟢 Ready' if api_configured else '🔴 Not Configured'}")


async def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Image Generation CLI Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate a single image
  python image_cli.py generate "A beautiful sunset over mountains"
  
  # Generate with specific style and aspect ratio
  python image_cli.py generate "Modern office workspace" --style photorealistic --aspect-ratio 16:9
  
  # Generate content images
  python image_cli.py content "Machine Learning Tutorial" --num-images 3
  
  # Show available options
  python image_cli.py styles
  python image_cli.py sizes
  python image_cli.py status
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Generate single image command
    generate_parser = subparsers.add_parser("generate", help="Generate a single image")
    generate_parser.add_argument("prompt", help="Image generation prompt")
    generate_parser.add_argument("--style", default="photorealistic", 
                               help="Image style (default: photorealistic)")
    generate_parser.add_argument("--aspect-ratio", default="16:9",
                               help="Aspect ratio (default: 16:9)")
    generate_parser.add_argument("--format", choices=["json", "text"], default="text",
                               help="Output format (default: text)")
    
    # Generate content images command
    content_parser = subparsers.add_parser("content", help="Generate content images")
    content_parser.add_argument("topic", help="Content topic")
    content_parser.add_argument("--num-images", type=int, default=3,
                              help="Number of images to generate (default: 3)")
    content_parser.add_argument("--content-type", default="blog_post",
                              help="Content type (default: blog_post)")
    content_parser.add_argument("--format", choices=["json", "text"], default="text",
                              help="Output format (default: text)")
    
    # Info commands
    subparsers.add_parser("styles", help="Show available styles")
    subparsers.add_parser("sizes", help="Show available aspect ratios")
    subparsers.add_parser("status", help="Show API configuration status")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Handle commands
    if args.command == "generate":
        success = await generate_single_image(
            prompt=args.prompt,
            style=args.style,
            aspect_ratio=args.aspect_ratio,
            output_format=args.format
        )
        sys.exit(0 if success else 1)
        
    elif args.command == "content":
        success = await generate_content_images(
            topic=args.topic,
            num_images=args.num_images,
            content_type=args.content_type,
            output_format=args.format
        )
        sys.exit(0 if success else 1)
        
    elif args.command == "styles":
        show_styles()
        
    elif args.command == "sizes":
        show_sizes()
        
    elif args.command == "status":
        show_status()


if __name__ == "__main__":
    asyncio.run(main())
