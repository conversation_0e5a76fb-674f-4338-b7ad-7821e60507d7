"""Image generation tool using OpenRouter models."""

import asyncio
import logging
from typing import List, Optional, Dict, Any
import httpx
import langsmith as ls
from langchain.tools import BaseTool
from pydantic import BaseModel, Field

from config.settings import settings

# Setup logger
logger = logging.getLogger(__name__)


class ImageGenerationRequest(BaseModel):
    """Request model for image generation."""
    prompt: str = Field(..., description="Image generation prompt")
    style: Optional[str] = Field(default="photorealistic", description="Image style preference")
    aspect_ratio: Optional[str] = Field(default="16:9", description="Image aspect ratio")
    quality: Optional[str] = Field(default="standard", description="Image quality setting")


class GeneratedImage(BaseModel):
    """Generated image information."""
    url: str = Field(..., description="Generated image URL")
    prompt: str = Field(..., description="Prompt used for generation")
    style: str = Field(..., description="Image style")
    aspect_ratio: str = Field(..., description="Image aspect ratio")
    alt_text: str = Field(..., description="SEO-optimized alt text")
    placement_section: Optional[str] = Field(None, description="Content section where image should be placed")


class ImageGeneratorTool:
    """Tool for generating images using APIIHUB image generation API."""
    
    # Available image styles
    AVAILABLE_STYLES = {
        "photorealistic": "realistic",
        "illustration": "illustration", 
        "infographic": "infographic",
        "diagram": "diagram",
        "screenshot": "screenshot",
        "realistic": "realistic",
        "artistic": "artistic",
        "cartoon": "cartoon"
    }
    
    # Available image sizes
    AVAILABLE_SIZES = {
        "1:1": "1024x1024",
        "16:9": "1792x1024",
        "9:16": "1024x1792",
        "4:3": "1536x1152",
        "3:4": "1152x1536",
        "square": "1024x1024",
        "landscape": "1792x1024",
        "portrait": "1024x1792"
    }
    
    def __init__(self):
        """Initialize the image generator with APIIHUB API."""
        if not settings.image_api_key:
            logger.warning("IMAGE_API_KEY not configured. Image generation will be disabled.")
            self.client = None
        else:
            self.client = httpx.AsyncClient(
                base_url=settings.image_api_base_url,
                headers={
                    "x-api-key": settings.image_api_key,
                    "Content-Type": "application/json"
                },
                timeout=120.0  # Image generation can take longer
            )
    
    @ls.traceable(name="generate_image")
    async def generate_image(
        self, 
        prompt: str,
        style: str = "photorealistic",
        aspect_ratio: str = "16:9",
        quality: str = "standard"
    ) -> GeneratedImage:
        """Generate an image using APIIHUB image generation API.
        
        Args:
            prompt: The image generation prompt
            style: Image style preference
            aspect_ratio: Image aspect ratio
            quality: Image quality setting (not used in current API)
            
        Returns:
            GeneratedImage with URL and metadata
            
        Raises:
            Exception: If image generation fails
        """
        try:
            # Check if API is configured
            if not self.client:
                raise Exception("Image generation API not configured (missing IMAGE_API_KEY)")
            
            # Map style to API format
            api_style = self.AVAILABLE_STYLES.get(style, "realistic")
            
            # Map aspect ratio to size
            size = self.AVAILABLE_SIZES.get(aspect_ratio, "1792x1024")
            
            # Enhance prompt for SEO content
            enhanced_prompt = self._enhance_prompt_for_seo(prompt, style)
            
            # Prepare request payload for APIIHUB API
            payload = {
                "text": enhanced_prompt,
                "style": api_style,
                "size": size
            }
            
            # Make API request
            response = await self.client.post("/api/v1/ai-tools/execute/text-to-image", json=payload)
            response.raise_for_status()
            result = response.json()
            
            # Check if request was successful
            if not result.get("success", False):
                error_msg = result.get("error", "Unknown error from image API")
                raise Exception(f"Image generation API error: {error_msg}")
            
            # Extract data from response
            data = result.get("data", {})
            image_url = data.get("image_url")
            model_used = data.get("model", "dall-e-3")
            execution_time = data.get("execution_time", 0)
            
            if not image_url:
                raise Exception("No image URL returned from API")
            
            # Generate SEO-optimized alt text
            alt_text = await self._generate_alt_text(prompt, style)
            
            generated_image = GeneratedImage(
                url=image_url,
                prompt=enhanced_prompt,
                style=style,
                aspect_ratio=aspect_ratio,
                alt_text=alt_text,
                placement_section=None
            )
            
            # Log successful generation with metrics
            logger.info(f"Image generated - Model: {model_used}, Style: {api_style}, "
                       f"Size: {size}, Time: {execution_time:.2f}s")
            
            return generated_image
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Image generation HTTP error - Status: {e.response.status_code}, "
                        f"Response: {e.response.text}")
            raise Exception(f"Image generation failed: HTTP {e.response.status_code}")
        except httpx.TimeoutException:
            logger.error("Image generation timed out")
            raise Exception("Image generation timed out")
        except Exception as e:
            logger.error(f"Image generation failed - Error: {str(e)}")
            raise Exception(f"Image generation failed: {str(e)}")
    
    def _enhance_prompt_for_seo(self, prompt: str, style: str) -> str:
        """Enhance the prompt for better SEO-focused images."""
        style_modifiers = {
            "photorealistic": "high-quality, professional, photorealistic",
            "illustration": "clean illustration, modern design, professional",
            "infographic": "clean infographic style, data visualization, professional layout",
            "diagram": "clear diagram, educational, well-organized layout",
            "screenshot": "clean interface, modern UI design, professional"
        }
        
        modifier = style_modifiers.get(style, "high-quality, professional")
        
        # Add SEO-friendly enhancements
        enhanced = f"{prompt}, {modifier}, web-friendly, engaging, high resolution, suitable for blog content"
        
        return enhanced
    
    async def _generate_alt_text(self, prompt: str, style: str) -> str:
        """Generate SEO-optimized alt text for the image."""
        # Simple alt text generation based on prompt
        # In a real implementation, you might use another LLM call
        
        # Extract key elements from prompt
        words = prompt.lower().split()
        
        # Filter out common words and focus on descriptive terms
        descriptive_words = [
            word for word in words 
            if word not in ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with']
            and len(word) > 2
        ]
        
        # Create alt text
        alt_text = f"{style} image showing {' '.join(descriptive_words[:5])}"
        
        return alt_text
    
    @ls.traceable(name="generate_content_images")
    async def generate_content_images(
        self,
        content_topic: str,
        content_type: str = "blog_post",
        num_images: int = 3
    ) -> List[GeneratedImage]:
        """Generate multiple images for content based on topic.
        
        Args:
            content_topic: Main topic of the content
            content_type: Type of content (blog_post, article, etc.)
            num_images: Number of images to generate
            
        Returns:
            List of generated images
        """
        images = []
        
        # Define different image types for content
        image_prompts = self._create_content_image_prompts(content_topic, content_type, num_images)
        
        # Generate images concurrently
        tasks = []
        for prompt_data in image_prompts:
            task = self.generate_image(
                prompt=prompt_data["prompt"],
                style=prompt_data["style"],
                aspect_ratio=prompt_data["aspect_ratio"]
            )
            tasks.append(task)
        
        # Wait for all images to be generated
        try:
            images = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out any failed generations
            successful_images = [
                img for img in images 
                if isinstance(img, GeneratedImage)
            ]
            
            return successful_images
            
        except Exception as e:
            logger.error(f"Batch image generation failed: {str(e)}")
            return []
    
    def _create_content_image_prompts(
        self, 
        topic: str, 
        content_type: str, 
        num_images: int
    ) -> List[Dict[str, str]]:
        """Create diverse image prompts for content."""
        
        base_prompts = [
            {
                "prompt": f"Hero image for {topic}",
                "style": "photorealistic",
                "aspect_ratio": "16:9"
            },
            {
                "prompt": f"Infographic elements for {topic}",
                "style": "infographic",
                "aspect_ratio": "4:3"
            },
            {
                "prompt": f"Step-by-step illustration for {topic}",
                "style": "illustration",
                "aspect_ratio": "16:9"
            },
            {
                "prompt": f"Statistical chart visualization for {topic}",
                "style": "diagram",
                "aspect_ratio": "4:3"
            },
            {
                "prompt": f"Professional example of {topic}",
                "style": "photorealistic",
                "aspect_ratio": "3:4"
            }
        ]
        
        # Return requested number of prompts
        return base_prompts[:num_images]
    
    async def close(self):
        """Close the HTTP client."""
        if self.client:
            await self.client.aclose()


# Factory function for easy instantiation
def create_image_generator() -> ImageGeneratorTool:
    """Create an image generator tool instance."""
    return ImageGeneratorTool() 