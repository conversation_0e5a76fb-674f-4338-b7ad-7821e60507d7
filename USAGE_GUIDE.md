# 图片生成API使用指南

由于MCP工具添加困难，这里提供了几种替代方案来让Cursor和Augment使用您的图片生成接口。

## 方案1：HTTP API服务器 (推荐)

### 启动服务器

```bash
# 安装依赖
pip install fastapi uvicorn

# 启动API服务器
python simple_api_server.py
```

服务器将在 `http://localhost:8000` 启动

### API端点

#### 1. 生成单张图片
```bash
curl -X POST "http://localhost:8000/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "style": "photorealistic",
    "aspect_ratio": "16:9"
  }'
```

#### 2. 生成内容图片
```bash
curl -X POST "http://localhost:8000/generate-content" \
  -H "Content-Type: application/json" \
  -d '{
    "content_topic": "Machine Learning Tutorial",
    "num_images": 3,
    "content_type": "blog_post"
  }'
```

#### 3. 获取可用样式
```bash
curl "http://localhost:8000/styles"
```

#### 4. 获取可用尺寸
```bash
curl "http://localhost:8000/sizes"
```

#### 5. 健康检查
```bash
curl "http://localhost:8000/health"
```

### 在Cursor中使用

1. 启动API服务器
2. 在Cursor中，您可以告诉AI助手：

```
请帮我生成一张图片，使用以下API：
POST http://localhost:8000/generate
请求体：{"prompt": "描述", "style": "photorealistic", "aspect_ratio": "16:9"}
```

### 在Augment中使用

在Augment中，您可以这样描述：

```
我有一个本地图片生成API运行在 http://localhost:8000
可以使用 POST /generate 端点生成图片
请求格式：{"prompt": "图片描述", "style": "样式", "aspect_ratio": "比例"}
```

## 方案2：命令行工具

### 基本使用

```bash
# 生成单张图片
python image_cli.py generate "A beautiful sunset over mountains"

# 指定样式和比例
python image_cli.py generate "Modern office workspace" --style photorealistic --aspect-ratio 16:9

# 生成内容图片
python image_cli.py content "Machine Learning Tutorial" --num-images 3

# JSON输出格式（便于程序处理）
python image_cli.py generate "Beautiful landscape" --format json
```

### 查看可用选项

```bash
# 查看可用样式
python image_cli.py styles

# 查看可用尺寸
python image_cli.py sizes

# 查看API状态
python image_cli.py status
```

### 在Cursor中使用命令行工具

您可以告诉Cursor：

```
我有一个图片生成命令行工具：
python image_cli.py generate "描述" --style 样式 --aspect-ratio 比例 --format json

请帮我使用这个工具生成图片
```

## 方案3：直接集成到代码中

### Python代码示例

```python
import asyncio
from tools.image_generator import create_image_generator

async def generate_image_example():
    # 创建图片生成器
    generator = create_image_generator()
    
    # 生成单张图片
    result = await generator.generate_image(
        prompt="A beautiful sunset over mountains",
        style="photorealistic",
        aspect_ratio="16:9"
    )
    
    print(f"图片URL: {result.url}")
    print(f"Alt文本: {result.alt_text}")
    
    # 生成内容图片
    content_images = await generator.generate_content_images(
        content_topic="Machine Learning Tutorial",
        num_images=3
    )
    
    for i, img in enumerate(content_images, 1):
        print(f"图片{i}: {img.url}")
    
    # 关闭连接
    await generator.close()

# 运行示例
asyncio.run(generate_image_example())
```

## 可用样式

- `photorealistic`: 照片级真实
- `illustration`: 插图风格
- `infographic`: 信息图表
- `diagram`: 图表/示意图
- `screenshot`: 截图风格
- `artistic`: 艺术风格
- `cartoon`: 卡通风格

## 可用尺寸比例

- `1:1`: 正方形 (1024x1024)
- `16:9`: 宽屏 (1792x1024)
- `9:16`: 竖屏 (1024x1792)
- `4:3`: 标准横向 (1536x1152)
- `3:4`: 标准竖向 (1152x1536)

## 故障排除

### 1. API密钥未配置
确保在 `.env` 文件中设置了 `IMAGE_API_KEY`：
```
IMAGE_API_KEY=your_api_key_here
IMAGE_API_BASE_URL=https://api.apiihub.com
```

### 2. 依赖缺失
安装所需依赖：
```bash
pip install fastapi uvicorn httpx langsmith langchain pydantic
```

### 3. 端口被占用
如果8000端口被占用，可以修改 `simple_api_server.py` 中的端口号：
```python
uvicorn.run("simple_api_server:app", host="0.0.0.0", port=8001)
```

## 推荐使用方式

1. **开发阶段**: 使用HTTP API服务器，便于调试和测试
2. **生产环境**: 可以将API服务器部署到云服务器
3. **脚本自动化**: 使用命令行工具
4. **代码集成**: 直接导入Python模块

选择最适合您工作流程的方案即可！
